# Backend

1. **Refactor blocking I/O**  
    - Replaced `fs.readFileSync` with `fs.promises.readFile` in `src/routes/items.js` to make the file reading operation non-blocking.

2. **Performance**  
    - Before refactoring, the stats api was at 0.3ms to 2ms loading speed. After -> 
    The downside of this is that it made the app more complex. But it's a good tradeoff for the performance boost.

3. **Testing**
    - Added unit tests for items routes (happy path + error cases) in `src/test/items.test.js`.


4. **Additiona - Validation**
    - The post route was missing validation, so I added it using express-validator.
    - I also added validation tests to `items.test.js`
