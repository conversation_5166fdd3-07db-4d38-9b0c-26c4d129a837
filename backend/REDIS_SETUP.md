# Redis Setup Guide

## Super Simple Setup (Recommended for Testing)

### 1. Install packages
```bash
npm install redis redis-memory-server
```

### 2. Just start the app!
```bash
npm start
```

The app will automatically:
- ✅ Try to connect to existing Redis (if you have it)
- ✅ Start an in-memory Redis server if none found
- ✅ Work perfectly either way!

## Alternative: Manual Redis Setup

#### macOS (using Homebrew)
```bash
brew install redis
brew services start redis
```

## Configuration

## Testing the Connection


## Graceful Degradation

The application is designed to work with or without Redis:

- **With Redis**: Full caching functionality for optimal performance
- **Without Redis**: Falls back to file-based operations (slower but functional)

## Cache Keys Structure

- `items:all` - All items array
- `item:{id}` - Individual item by ID
- `stats:current` - Computed statistics
- `search:q:{query}:limit:{limit}` - Search results

## Monitoring

### Check Redis Connection
```bash
redis-cli ping
```
Should return: `PONG`

### View Cached Data
```bash
redis-cli
> KEYS *
> GET items:all
```

## Next Steps

1. Install Redis package: `npm install redis`
2. Start Redis server
3. Start the application
4. Implement caching in routes (next phase)
