# Redis Setup Guide

## Installation

### 1. Install Redis Package
```bash
npm install redis
```

### 2. Install and Start Redis Server

#### macOS (using Homebrew)
```bash
brew install redis
brew services start redis
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

#### Windows
Download and install from: https://redis.io/download

#### Docker (Alternative)
```bash
docker run -d -p 6379:6379 --name redis redis:alpine
```

## Configuration

### Environment Variables
Copy `.env.example` to `.env` and adjust if needed:
```bash
cp .env.example .env
```

Default configuration:
- Redis Host: `localhost`
- Redis Port: `6379`
- Cache TTL: `300` seconds (5 minutes)

## Testing the Connection

### 1. Start the server
```bash
npm start
```

You should see:
```
Backend running on http://localhost:3001
Cache service status: Connected
```

### 2. Run Redis tests
```bash
npm test -- redis.test.js
```

## Graceful Degradation

The application is designed to work with or without Redis:

- **With Redis**: Full caching functionality for optimal performance
- **Without Redis**: Falls back to file-based operations (slower but functional)

## Cache Keys Structure

- `items:all` - All items array
- `item:{id}` - Individual item by ID
- `stats:current` - Computed statistics
- `search:q:{query}:limit:{limit}` - Search results

## Monitoring

### Check Redis Connection
```bash
redis-cli ping
```
Should return: `PONG`

### View Cached Data
```bash
redis-cli
> KEYS *
> GET items:all
```

## Next Steps

1. Install Redis package: `npm install redis`
2. Start Redis server
3. Start the application
4. Implement caching in routes (next phase)
