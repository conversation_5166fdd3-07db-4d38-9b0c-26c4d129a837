const redis = require('redis');

class RedisClient {
  constructor() {
    this.client = null;
    this.isConnected = false;
  }

  async connect() {
    try {
      // Create Redis client with default connection (localhost:6379)
      this.client = redis.createClient({
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        // Add retry strategy
        retry_strategy: (options) => {
          if (options.error && options.error.code === 'ECONNREFUSED') {
            console.error('Redis connection refused');
            return new Error('Redis server connection refused');
          }
          if (options.total_retry_time > 1000 * 60 * 60) {
            console.error('Redis retry time exhausted');
            return new Error('Retry time exhausted');
          }
          if (options.attempt > 10) {
            console.error('Redis max attempts reached');
            return undefined;
          }
          // Reconnect after
          return Math.min(options.attempt * 100, 3000);
        }
      });

      // Handle connection events
      this.client.on('connect', () => {
        console.log('Redis client connected');
        this.isConnected = true;
      });

      this.client.on('error', (err) => {
        console.error('Redis client error:', err);
        this.isConnected = false;
      });

      this.client.on('end', () => {
        console.log('Redis client disconnected');
        this.isConnected = false;
      });

      // Connect to Redis
      await this.client.connect();
      
      return this.client;
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect() {
    if (this.client) {
      await this.client.quit();
      this.isConnected = false;
    }
  }

  // Helper method to check if Redis is available
  isAvailable() {
    return this.isConnected && this.client;
  }

  // Get the client instance
  getClient() {
    return this.client;
  }

  // Wrapper methods for common operations
  async get(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis client not available');
    }
    return await this.client.get(key);
  }

  async set(key, value, options = {}) {
    if (!this.isAvailable()) {
      throw new Error('Redis client not available');
    }
    return await this.client.set(key, value, options);
  }

  async del(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis client not available');
    }
    return await this.client.del(key);
  }

  async exists(key) {
    if (!this.isAvailable()) {
      throw new Error('Redis client not available');
    }
    return await this.client.exists(key);
  }

  // JSON helper methods
  async getJSON(key) {
    const value = await this.get(key);
    return value ? JSON.parse(value) : null;
  }

  async setJSON(key, value, options = {}) {
    return await this.set(key, JSON.stringify(value), options);
  }
}

// Create singleton instance
const redisClient = new RedisClient();

module.exports = redisClient;
