const { RedisMemoryServer } = require('redis-memory-server');

class AutoRedis {
  constructor() {
    this.redisServer = null;
  }

  async start() {
    try {
      // Try to connect to existing Redis first
      const redis = require('redis');
      const testClient = redis.createClient({
        host: 'localhost',
        port: 6379,
        connect_timeout: 2000
      });

      await testClient.connect();
      await testClient.ping();
      await testClient.quit();
      
      console.log('✅ Found existing Redis server');
      return { host: 'localhost', port: 6379 };
      
    } catch (error) {
      // No existing Redis, start in-memory one
      console.log('🚀 Starting in-memory Redis server...');
      
      this.redisServer = new RedisMemoryServer({
        instance: {
          port: 6379,
        },
      });
      
      const host = await this.redisServer.getHost();
      const port = await this.redisServer.getPort();
      
      console.log(`✅ In-memory Redis started at ${host}:${port}`);
      return { host, port };
    }
  }

  async stop() {
    if (this.redisServer) {
      await this.redisServer.stop();
      console.log('🛑 In-memory Redis stopped');
    }
  }
}

module.exports = new AutoRedis();
