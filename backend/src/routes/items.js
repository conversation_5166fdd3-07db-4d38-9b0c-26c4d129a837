const express = require('express');
const fs = require('fs');
const path = require('path');
const { validateItem, handleValidationErrors } = require('../validators/itemValidators');
const cacheService = require('../services/cacheService');
const router = express.Router();
const DATA_PATH = path.join(__dirname, '../../../data/items.json');

// GET /api/items
router.get('/', async (req, res, next) => {
  try {
    const data = await cacheService.getItems(DATA_PATH);
    const { limit, q } = req.query;
    let results = data;

    if (q) {
      results = results.filter(item => item.name.toLowerCase().includes(q.toLowerCase()));
    }

    if (limit) {
      results = results.slice(0, parseInt(limit));
    }

    res.json(results);
  } catch (err) {
    next(err);
  }
});

// GET /api/items/:id
router.get('/:id', async (req, res, next) => {
  try {
    const data = await cacheService.getItems(DATA_PATH);
    const item = data.find(i => i.id === parseInt(req.params.id));
    if (!item) {
      const err = new Error('Item not found');
      err.status = 404;
      throw err;
    }
    res.json(item);
  } catch (err) {
    next(err);
  }
});

// POST /api/items
router.post('/', validateItem, handleValidationErrors, async (req, res, next) => {
  try {
    const item = req.body;

    // Get current data
    const data = await cacheService.getItems(DATA_PATH);
    item.id = Date.now();
    data.push(item);

    // Write to file (persistence)
    fs.writeFileSync(DATA_PATH, JSON.stringify(data, null, 2));

    // Update Redis cache
    await cacheService.setItems(data);

    res.status(201).json(item);
  } catch (err) {
    next(err);
  }
});

module.exports = router;