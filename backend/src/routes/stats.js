const express = require('express');
const fs = require('fs').promises;
const path = require('path');
const cacheService = require('../services/cacheService');
const router = express.Router();
const DATA_PATH = path.join(__dirname, '../../../data/items.json');

// Helper function to calculate stats from items
function calculateStats(items) {
  if (!items || items.length === 0) {
    return { total: 0, averagePrice: 0 };
  }

  return {
    total: items.length,
    averagePrice: items.reduce((acc, cur) => acc + (cur.price || 0), 0) / items.length
  };
}

// Helper function to read items from file
async function readItemsFromFile() {
  try {
    const raw = await fs.readFile(DATA_PATH, 'utf8');
    return JSON.parse(raw);
  } catch (error) {
    console.error('Error reading items file:', error);
    throw error;
  }
}

// GET /api/stats with Redis caching
router.get('/', async (req, res, next) => {
  try {
    // Try to get stats from cache first
    let stats = await cacheService.getStats();

    if (stats) {
      console.log('📊 Stats served from cache');
      return res.json(stats);
    }

    // Cache miss - calculate stats from file
    console.log('📊 Stats cache miss - calculating from file');
    const items = await readItemsFromFile();
    stats = calculateStats(items);

    // Cache the calculated stats (TTL: 5 minutes)
    await cacheService.setStats(stats, 300);

    res.json(stats);
  } catch (error) {
    next(error);
  }
});

// POST /api/stats/invalidate - for manual cache clearing (useful for testing)
router.post('/invalidate', async (req, res, next) => {
  try {
    const success = await cacheService.invalidateAllItems();
    res.json({
      success,
      message: success ? 'Cache invalidated successfully' : 'Cache invalidation failed or cache not available'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;