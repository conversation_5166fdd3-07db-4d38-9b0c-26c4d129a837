const express = require('express');
const path = require('path');
const cacheService = require('../services/cacheService');
const router = express.Router();
const DATA_PATH = path.join(__dirname, '../../../data/items.json');

// GET /api/stats with Redis caching and file fallback
router.get('/', async (req, res, next) => {
  try {
    const stats = await cacheService.getStats(DATA_PATH);
    res.json(stats);
  } catch (error) {
    next(error);
  }
});

module.exports = router;