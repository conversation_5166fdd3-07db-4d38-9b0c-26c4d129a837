const express = require('express');
const path = require('path');
const cacheService = require('../services/cacheService');
const router = express.Router();
const DATA_PATH = path.join(__dirname, '../../../data/items.json');

// GET /api/stats with Redis caching and file fallback
router.get('/', async (req, res, next) => {
  try {
    // Get stats from cache with automatic file fallback
    const stats = await cacheService.getStats(DATA_PATH);
    res.json(stats);
  } catch (error) {
    next(error);
  }
});

// POST /api/stats/invalidate - for manual cache clearing (useful for testing)
router.post('/invalidate', async (req, res, next) => {
  try {
    const success = await cacheService.invalidateAllItems();
    res.json({
      success,
      message: success ? 'Cache invalidated successfully' : 'Cache invalidation failed or cache not available'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;