const express = require('express');
const path = require('path');
const morgan = require('morgan');
const itemsRouter = require('./routes/items');
const statsRouter = require('./routes/stats');
const cors = require('cors');
const { getCookie, notFound } = require('./middleware/errorHandler');
const cacheService = require('./services/cacheService');
const autoRedis = require('./utils/autoRedis');

const DATA_PATH = path.join(__dirname, '../../data/items.json');

const app = express();
const port = process.env.PORT || 3001;

app.use(cors({ origin: 'http://localhost:3000' }));
// Basic middleware
app.use(express.json());
app.use(morgan('dev'));

// Routes
app.use('/api/items', itemsRouter);
app.use('/api/stats', statsRouter);

// Not Found
app.use('*', notFound);

getCookie();

// Initialize cache service
async function startServer() {
  try {
    // Auto-start Redis if needed
    await autoRedis.start();

    // Initialize cache (Redis connection)
    await cacheService.init();

    // Load initial data into Redis
    await cacheService.loadInitialData(DATA_PATH);

    app.listen(port, () => {
      console.log('Backend running on http://localhost:' + port);
      console.log('Cache service status:', cacheService.isAvailable() ? 'Connected' : 'Disabled');
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await cacheService.shutdown();
  await autoRedis.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await cacheService.shutdown();
  await autoRedis.stop();
  process.exit(0);
});

startServer();