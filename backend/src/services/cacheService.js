const redisClient = require('../utils/redis');

class CacheService {
  constructor() {
    this.defaultTTL = 300; // 5 minutes default TTL
    this.keys = {
      ALL_ITEMS: 'items:all',
      ITEM_BY_ID: 'item:',
      STATS: 'stats:current',
      SEARCH: 'search:'
    };
  }

  // Initialize cache service
  async init() {
    try {
      await redisClient.connect();
      console.log('Cache service initialized');
      return true;
    } catch (error) {
      console.warn('Cache service failed to initialize, falling back to no cache:', error.message);
      return false;
    }
  }

  // Check if cache is available
  isAvailable() {
    return redisClient.isAvailable();
  }

  // Items caching methods
  async getAllItems() {
    if (!this.isAvailable()) return null;
    
    try {
      return await redisClient.getJSON(this.keys.ALL_ITEMS);
    } catch (error) {
      console.error('Error getting items from cache:', error);
      return null;
    }
  }

  async setAllItems(items, ttl = this.defaultTTL) {
    if (!this.isAvailable()) return false;
    
    try {
      await redisClient.setJSON(this.keys.ALL_ITEMS, items, { EX: ttl });
      return true;
    } catch (error) {
      console.error('Error setting items in cache:', error);
      return false;
    }
  }

  async getItemById(id) {
    if (!this.isAvailable()) return null;
    
    try {
      return await redisClient.getJSON(this.keys.ITEM_BY_ID + id);
    } catch (error) {
      console.error('Error getting item from cache:', error);
      return null;
    }
  }

  async setItemById(id, item, ttl = this.defaultTTL) {
    if (!this.isAvailable()) return false;
    
    try {
      await redisClient.setJSON(this.keys.ITEM_BY_ID + id, item, { EX: ttl });
      return true;
    } catch (error) {
      console.error('Error setting item in cache:', error);
      return false;
    }
  }

  // Stats caching methods
  async getStats() {
    if (!this.isAvailable()) return null;
    
    try {
      return await redisClient.getJSON(this.keys.STATS);
    } catch (error) {
      console.error('Error getting stats from cache:', error);
      return null;
    }
  }

  async setStats(stats, ttl = this.defaultTTL) {
    if (!this.isAvailable()) return false;
    
    try {
      await redisClient.setJSON(this.keys.STATS, stats, { EX: ttl });
      return true;
    } catch (error) {
      console.error('Error setting stats in cache:', error);
      return false;
    }
  }

  // Search results caching
  async getSearchResults(query, limit) {
    if (!this.isAvailable()) return null;
    
    const key = this.keys.SEARCH + `q:${query}:limit:${limit}`;
    try {
      return await redisClient.getJSON(key);
    } catch (error) {
      console.error('Error getting search results from cache:', error);
      return null;
    }
  }

  async setSearchResults(query, limit, results, ttl = this.defaultTTL) {
    if (!this.isAvailable()) return false;
    
    const key = this.keys.SEARCH + `q:${query}:limit:${limit}`;
    try {
      await redisClient.setJSON(key, results, { EX: ttl });
      return true;
    } catch (error) {
      console.error('Error setting search results in cache:', error);
      return false;
    }
  }

  // Cache invalidation methods
  async invalidateAllItems() {
    if (!this.isAvailable()) return false;
    
    try {
      // Delete all items cache
      await redisClient.del(this.keys.ALL_ITEMS);
      
      // Delete all individual item caches
      const client = redisClient.getClient();
      const itemKeys = await client.keys(this.keys.ITEM_BY_ID + '*');
      if (itemKeys.length > 0) {
        await client.del(itemKeys);
      }
      
      // Delete all search caches
      const searchKeys = await client.keys(this.keys.SEARCH + '*');
      if (searchKeys.length > 0) {
        await client.del(searchKeys);
      }
      
      // Delete stats cache
      await redisClient.del(this.keys.STATS);
      
      console.log('Cache invalidated');
      return true;
    } catch (error) {
      console.error('Error invalidating cache:', error);
      return false;
    }
  }

  // Graceful shutdown
  async shutdown() {
    try {
      await redisClient.disconnect();
      console.log('Cache service shutdown');
    } catch (error) {
      console.error('Error shutting down cache service:', error);
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
