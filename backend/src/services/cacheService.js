const redisClient = require('../utils/redis');
const fs = require('fs').promises;

class CacheService {
  constructor() {
    this.ITEMS_KEY = 'items:all';
    this.STATS_KEY = 'stats:current';
  }

  // Initialize and load data
  async init() {
    try {
      await redisClient.connect();
      console.log('Cache service initialized');
      return true;
    } catch (error) {
      console.warn('Cache service failed to initialize, falling back to file-only:', error.message);
      return false;
    }
  }

  async loadInitialData(filePath) {
    if (!redisClient.isAvailable()) return false;

    try {
      const raw = await fs.readFile(filePath, 'utf8');
      const items = JSON.parse(raw);

      await redisClient.setJSON(this.ITEMS_KEY, items);
      await redisClient.setJSON(this.STATS_KEY, this.calculateStats(items));

      console.log(`🚀 Loaded ${items.length} items into Redis`);
      return true;
    } catch (error) {
      console.error('Failed to load initial data:', error);
      return false;
    }
  }

  // Simple get/set for items
  async getItems(filePath) {
    // Try Redis first
    if (redisClient.isAvailable()) {
      try {
        const items = await redisClient.getJSON(this.ITEMS_KEY);
        if (items) return items;
      } catch (error) {
        console.warn('Redis error, falling back to file:', error.message);
      }
    }

    // Fallback to file
    const raw = await fs.readFile(filePath, 'utf8');
    return JSON.parse(raw);
  }

  async setItems(items) {
    if (redisClient.isAvailable()) {
      try {
        await redisClient.setJSON(this.ITEMS_KEY, items);
        await redisClient.setJSON(this.STATS_KEY, this.calculateStats(items));
      } catch (error) {
        console.warn('Failed to update Redis cache:', error.message);
      }
    }
  }

  // Simple get for stats
  async getStats(filePath) {
    // Try Redis first
    if (redisClient.isAvailable()) {
      try {
        const stats = await redisClient.getJSON(this.STATS_KEY);
        if (stats) return stats;
      } catch (error) {
        console.warn('Redis error, calculating from file:', error.message);
      }
    }

    // Fallback: calculate from items
    const items = await this.getItems(filePath);
    return this.calculateStats(items);
  }

  // Cache pre calculated stats
  calculateStats(items) {
    if (!items?.length) return { total: 0, averagePrice: 0 };

    return {
      total: items.length,
      averagePrice: items.reduce((acc, cur) => acc + (cur.price || 0), 0) / items.length
    };
  }

  // Cleanup
  async shutdown() {
    try {
      await redisClient.disconnect();
    } catch (error) {
      console.error('Error shutting down cache:', error);
    }
  }
}

// Create singleton instance
const cacheService = new CacheService();

module.exports = cacheService;
