const request = require('supertest');
const express = require('express');
const itemsRouter = require('../routes/items');

// Create test app
const app = express();
app.use(express.json());
app.use('/api/items', itemsRouter);

// Add basic error handler
app.use((err, req, res, next) => {
  res.status(err.status || 500).json({ error: err.message });
});

describe('Items API', () => {
  describe('GET /api/items', () => {
    test('should return all items', async () => {
      const response = await request(app)
        .get('/api/items')
        .expect(200);

      // Test with real data from items.json
      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeGreaterThan(0);

      // Check structure of first item
      expect(response.body[0]).toHaveProperty('id');
      expect(response.body[0]).toHaveProperty('name');
      expect(response.body[0]).toHaveProperty('category');
      expect(response.body[0]).toHaveProperty('price');
    });

    test('should filter items by search query', async () => {
      const response = await request(app)
        .get('/api/items?q=laptop')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      // Should find items containing "laptop" (case insensitive)
      response.body.forEach(item => {
        expect(item.name.toLowerCase()).toContain('laptop');
      });
    });

    test('should limit number of items returned', async () => {
      const response = await request(app)
        .get('/api/items?limit=2')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(2);
    });

    test('should combine search and limit', async () => {
      const response = await request(app)
        .get('/api/items?q=electronics&limit=1')
        .expect(200);

      expect(Array.isArray(response.body)).toBe(true);
      expect(response.body.length).toBeLessThanOrEqual(1);
    });
  });

  describe('GET /api/items/:id', () => {
    test('should return item by id', async () => {
      const response = await request(app)
        .get('/api/items/1')
        .expect(200);

      expect(response.body).toHaveProperty('id', 1);
      expect(response.body).toHaveProperty('name');
      expect(response.body).toHaveProperty('category');
      expect(response.body).toHaveProperty('price');
    });

    test('should return 404 for non-existent item', async () => {
      const response = await request(app)
        .get('/api/items/99999')
        .expect(404);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Item not found');
    });

    test('should handle invalid id format', async () => {
      const response = await request(app)
        .get('/api/items/invalid')
        .expect(404);

      // parseInt('invalid') returns NaN, so item won't be found
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('POST /api/items', () => {
    test('should create new item with valid data', async () => {
      const newItem = {
        name: 'Test Item',
        category: 'Test Category',
        price: 100
      };

      const response = await request(app)
        .post('/api/items')
        .send(newItem)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(newItem.name);
      expect(response.body.category).toBe(newItem.category);
      expect(response.body.price).toBe(newItem.price);
    });

    test('should create item without optional category', async () => {
      const newItem = {
        name: 'Test Item',
        price: 50
      };

      const response = await request(app)
        .post('/api/items')
        .send(newItem)
        .expect(201);

      expect(response.body).toHaveProperty('id');
      expect(response.body.name).toBe(newItem.name);
      expect(response.body.price).toBe(newItem.price);
    });

    test('should return 400 for missing name', async () => {
      const invalidItem = {
        category: 'Test Category',
        price: 100
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Name is required'
          })
        ])
      );
    });

    test('should return 400 for missing price', async () => {
      const invalidItem = {
        name: 'Test Item',
        category: 'Test Category'
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Price must be a number'
          })
        ])
      );
    });

    test('should return 400 for negative price', async () => {
      const invalidItem = {
        name: 'Test Item',
        category: 'Test Category',
        price: -10
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Price must be a positive number'
          })
        ])
      );
    });

    test('should return 400 for non-numeric price', async () => {
      const invalidItem = {
        name: 'Test Item',
        category: 'Test Category',
        price: 'invalid'
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Price must be a number'
          })
        ])
      );
    });

    test('should return 400 for empty name', async () => {
      const invalidItem = {
        name: '',
        category: 'Test Category',
        price: 100
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Name is required'
          })
        ])
      );
    });

    test('should return 400 for name too long', async () => {
      const invalidItem = {
        name: 'a'.repeat(101), // 101 characters
        category: 'Test Category',
        price: 100
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Name must be between 1 and 100 characters'
          })
        ])
      );
    });

    test('should return 400 for category too long', async () => {
      const invalidItem = {
        name: 'Test Item',
        category: 'a'.repeat(101), // 101 characters
        price: 100
      };

      const response = await request(app)
        .post('/api/items')
        .send(invalidItem)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      expect(response.body.details).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            msg: 'Category must not exceed 100 characters'
          })
        ])
      );
    });

    test('should handle empty request body', async () => {
      const response = await request(app)
        .post('/api/items')
        .send({})
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
      expect(response.body).toHaveProperty('details');
      // Should have errors for both missing name and missing price
      expect(response.body.details.length).toBeGreaterThanOrEqual(2);
    });
  });
});