const cacheService = require('../services/cacheService');

describe('Redis Connection', () => {
  beforeAll(async () => {
    // Initialize cache service for testing
    await cacheService.init();
  });

  afterAll(async () => {
    // Clean up
    await cacheService.shutdown();
  });

  test('should connect to Redis', () => {
    // This test will pass even if Redis is not available
    // The cache service gracefully handles Redis unavailability
    expect(typeof cacheService.isAvailable()).toBe('boolean');
  });

  test('should handle cache operations gracefully when Redis is unavailable', async () => {
    // These should not throw errors even if Redis is down
    const result1 = await cacheService.getAllItems();
    const result2 = await cacheService.setAllItems([{ id: 1, name: 'test' }]);
    
    // Should return null/false when Redis is unavailable, not throw errors
    expect(result1 === null || Array.isArray(result1)).toBe(true);
    expect(typeof result2).toBe('boolean');
  });

  // Only run Redis-specific tests if Redis is available
  describe('Redis Operations (when available)', () => {
    beforeEach(() => {
      // Skip these tests if Redis is not available
      if (!cacheService.isAvailable()) {
        console.log('Skipping Redis tests - Redis not available');
        return;
      }
    });

    test('should set and get JSON data', async () => {
      if (!cacheService.isAvailable()) return;

      const testData = { id: 1, name: 'Test Item', price: 100 };
      
      await cacheService.setAllItems([testData]);
      const result = await cacheService.getAllItems();
      
      expect(result).toEqual([testData]);
    });

    test('should handle cache invalidation', async () => {
      if (!cacheService.isAvailable()) return;

      const testData = [{ id: 1, name: 'Test Item' }];
      
      await cacheService.setAllItems(testData);
      await cacheService.invalidateAllItems();
      
      const result = await cacheService.getAllItems();
      expect(result).toBeNull();
    });
  });
});
